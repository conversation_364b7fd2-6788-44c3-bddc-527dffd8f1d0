import json, sys
from collections import defaultdict

def merge(schema, data, path=""):
    t = type(data).__name__
    schema[path or "$"].add(t)
    if isinstance(data, dict):
        for k, v in data.items():
            merge(schema, v, f"{path}.{k}" if path else k)
    elif isinstance(data, list):
        for i, v in enumerate(data[:200]):  # семплируем
            merge(schema, v, f"{path}[]")
    return schema

with open("file.json", "r", encoding="utf-8") as f:
    data = json.load(f)

schema = merge(defaultdict(set), data)
for k in sorted(schema):
    types = ", ".join(sorted(schema[k]))
    print(f"{k}: {types}")
