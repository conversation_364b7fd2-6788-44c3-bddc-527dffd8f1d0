model:
  policy_model:
    ckpt: ../models/t5-large-1k-train
    input_padding_side: right
    train_generation_kwargs:
      do_sample: True
      top_k: 20
      top_p: null
      temperature: 0.7
    eval_generation_kwargs:
      do_sample: False
      num_beams: 1
  value_model:
    ckpt: t5-base
    freeze_value_model: False
    policy_value_sharing: False
  

reward:
#  relevance_model:
#    ckpt: ./tasks/qa_feedback/models/rel_rm
#    positive_reward: 0.3
#    negative_reward: -0.3
#  factuality_model:
#    ckpt: ../models/fact_rm
#    positive_reward: 0.5
#    negative_reward: -0.5
#  completeness_model:
#    ckpt: ./tasks/qa_feedback/models/comp_rm
#    mean: -0.44677690555995353
#    std: 8.301160619054132
#    bias: 0.0
#    scale: 0.3
#  factuality_compact_model:
#    ckpt: ../models/prm_longformer_compact_ori_test
#    positive_reward: 0.5
#    negative_reward: -0.5
  calculation_error_model:
    ckpt: ../models/prm_longformer_Calculation-Error
    positive_reward: 0.5
    negative_reward: -0.5
  context_inconsistency_model:
    ckpt: ../models/prm_longformer_Context-Inconsistency
    positive_reward: 0.5
    negative_reward: -0.5
  fabrication_model:
    ckpt: ../models/prm_longformer_Fabrication
    positive_reward: 0.5
    negative_reward: -0.5
  factual_inconsistency_model:
    ckpt: ../models/prm_longformer_Factual-Inconsistency
    positive_reward: 0.5
    negative_reward: -0.5
  instruction_inconsistency_model:
    ckpt: ../models/prm_longformer_Instruction-Inconsistency
    positive_reward: 0.5
    negative_reward: -0.5
  logical_inconsistency_model:
    ckpt: ../models/prm_longformer_Logical-Inconsistency
    positive_reward: 0.5
    negative_reward: -0.5

env:
  max_input_len: 1024
  max_generated_len: 200
  train_num_samples_per_input: 4

ppo:
  kl_coef: 0.3
  lam: 0.95
  gamma: 1.0
  pg_coef: 1.0
  vf_coef: 1.0
  cliprange: 0.2
  cliprange_value: 0.2
  whiten_rewards: True

train:
  total_episodes: 80000
  eval_interval: 50
  sampling_batch_size_per_card: 8
  training_batch_size_per_card: 4
  lr: 0.00001
  n_warmup_steps: 100
  n_ppo_epoch_per_rollout: 4
  kl_threshold: 10.0
  clip_grad: False
  max_grad_norm: 0.5
  seed: 42
  cuda_deterministic: True

logging:
  run_name: finegrained
  wandb_log: True
  wandb_entity: rxl190028
  wandb_project: RLHF
  log_interval: 1
  save_dir: ../models/fine_grained

